/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CodeEditor: typeof import('./src/components/scripts/CodeEditor.vue')['default']
    ConfirmDialog: typeof import('./src/components/common/ConfirmDialog.vue')['default']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElLoadingService: typeof import('element-plus/es')['ElLoadingService']
    LoadingCard: typeof import('./src/components/common/LoadingCard.vue')['default']
    PageHeader: typeof import('./src/components/common/PageHeader.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RuntimeBadge: typeof import('./src/components/scripts/RuntimeBadge.vue')['default']
    SidebarMenu: typeof import('./src/components/layout/SidebarMenu.vue')['default']
    StatusTag: typeof import('./src/components/common/StatusTag.vue')['default']
    TaskProgress: typeof import('./src/components/scripts/TaskProgress.vue')['default']
    TaskStatusBadge: typeof import('./src/components/scripts/TaskStatusBadge.vue')['default']
  }
}
