<template>
  <div class="task-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">脚本任务管理</h2>
        <p class="page-description">管理和监控脚本执行任务的状态和结果</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/scripts/execute')">
          <el-icon><VideoPlay /></el-icon>
          执行脚本
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-select
          v-model="filterStatus"
          placeholder="任务状态"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="等待中" value="pending" />
          <el-option label="运行中" value="running" />
          <el-option label="已完成" value="completed" />
          <el-option label="失败" value="failed" />
          <el-option label="已取消" value="canceled" />
        </el-select>
        
        <el-select
          v-model="filterAppId"
          placeholder="应用"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部应用" value="" />
          <!-- TODO: 从应用列表动态加载 -->
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleFilter"
          class="date-picker"
        />
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="showBatchActions = !showBatchActions">
          <el-icon><Operation /></el-icon>
          批量操作
        </el-button>
      </div>
    </div>
    
    <!-- 批量操作栏 -->
    <div v-show="showBatchActions" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedTasks.length }} 个任务
      </div>
      <div class="batch-buttons">
        <el-button size="small" @click="batchCancel">批量取消</el-button>
        <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>
    
    <!-- 任务列表 -->
    <div class="task-list">
      <el-table
        v-loading="scriptsStore.taskLoading"
        :data="scriptsStore.tasks"
        @selection-change="handleSelectionChange"
        class="task-table"
        empty-text="暂无任务数据"
      >
        <el-table-column 
          v-if="showBatchActions"
          type="selection" 
          width="55" 
        />
        
        <el-table-column label="任务信息" min-width="200">
          <template #default="{ row }">
            <div class="task-info">
              <div class="task-id">{{ row.id }}</div>
              <div class="task-script">{{ row.script_path }}</div>
              <div class="task-app">应用: {{ row.app_id }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="运行时" width="100">
          <template #default="{ row }">
            <el-tag :type="getRuntimeTagType(row.runtime_type)">
              {{ getRuntimeLabel(row.runtime_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)" size="small">
              {{ row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="执行时间" width="120">
          <template #default="{ row }">
            <div v-if="row.duration" class="duration">
              {{ formatDuration(row.duration) }}
            </div>
            <div v-else class="text-placeholder">-</div>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            <span class="create-time">
              {{ formatTime(row.created_at) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                type="primary" 
                @click="viewTask(row.id)"
              >
                查看
              </el-button>
              
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      command="cancel" 
                      :disabled="!canCancel(row.status)"
                    >
                      <el-icon><Close /></el-icon>取消
                    </el-dropdown-item>
                    <el-dropdown-item command="logs">
                      <el-icon><Document /></el-icon>查看日志
                    </el-dropdown-item>
                    <el-dropdown-item command="result" :disabled="row.status !== 'completed'">
                      <el-icon><Download /></el-icon>下载结果
                    </el-dropdown-item>
                    <el-dropdown-item divided command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="scriptsStore.taskPagination.page"
          v-model:page-size="scriptsStore.taskPagination.pageSize"
          :total="scriptsStore.taskPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useScriptsStore } from '@/stores/scripts'
import type { ScriptTask } from '@/types'
import dayjs from 'dayjs'

// 响应式数据
const router = useRouter()
const scriptsStore = useScriptsStore()
const showBatchActions = ref(false)
const filterStatus = ref('')
const filterAppId = ref('')
const dateRange = ref<[string, string] | null>(null)
const selectedTasks = ref<ScriptTask[]>([])

// 计算属性
const queryParams = computed(() => ({
  page: scriptsStore.taskPagination.page,
  pageSize: scriptsStore.taskPagination.pageSize,
  status: filterStatus.value || undefined,
  app_id: filterAppId.value || undefined,
  start_time: dateRange.value?.[0],
  end_time: dateRange.value?.[1]
}))

// 方法定义
const loadTaskList = async () => {
  try {
    await scriptsStore.fetchTasks(queryParams.value)
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  }
}

const handleFilter = () => {
  scriptsStore.taskPagination.page = 1
  loadTaskList()
}

const refreshList = () => {
  loadTaskList()
}

const handleSelectionChange = (selection: ScriptTask[]) => {
  selectedTasks.value = selection
}

const handleSizeChange = (size: number) => {
  scriptsStore.taskPagination.pageSize = size
  scriptsStore.taskPagination.page = 1
  loadTaskList()
}

const handleCurrentChange = (page: number) => {
  scriptsStore.taskPagination.page = page
  loadTaskList()
}

const viewTask = (id: string) => {
  router.push(`/scripts/tasks/${id}`)
}

const handleAction = async (command: string, task: ScriptTask) => {
  switch (command) {
    case 'cancel':
      await cancelTask(task)
      break
    case 'logs':
      viewTaskLogs(task)
      break
    case 'result':
      downloadTaskResult(task)
      break
    case 'delete':
      await deleteTask(task)
      break
  }
}

const cancelTask = async (task: ScriptTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.id}" 吗？`,
      '确认取消',
      { type: 'warning' }
    )
    
    await scriptsStore.cancelTask(task.id)
    ElMessage.success('任务已取消')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const viewTaskLogs = (task: ScriptTask) => {
  router.push(`/scripts/tasks/${task.id}?tab=logs`)
}

const downloadTaskResult = async (task: ScriptTask) => {
  // TODO: 实现结果下载
  ElMessage.info('结果下载功能开发中...')
}

const deleteTask = async (task: ScriptTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.id}" 吗？此操作不可恢复！`,
      '确认删除',
      { 
        type: 'error',
        confirmButtonText: '确定删除',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    // TODO: 实现任务删除API
    ElMessage.success('任务已删除')
    await loadTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

// 批量操作
const batchCancel = async () => {
  // TODO: 实现批量取消
  ElMessage.info('批量取消功能开发中...')
}

const batchDelete = async () => {
  // TODO: 实现批量删除
  ElMessage.info('批量删除功能开发中...')
}

// 工具函数
const getRuntimeTagType = (runtime: string) => {
  const typeMap: Record<string, string> = {
    python: 'warning',
    nodejs: 'success',
    go: 'info',
    java: 'danger'
  }
  return typeMap[runtime] || ''
}

const getRuntimeLabel = (runtime: string) => {
  const labelMap: Record<string, string> = {
    python: 'Python',
    nodejs: 'Node.js',
    go: 'Go',
    java: 'Java'
  }
  return labelMap[runtime] || runtime
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    canceled: ''
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    canceled: '已取消'
  }
  return labelMap[status] || status
}

const getPriorityTagType = (priority: number) => {
  if (priority >= 8) return 'danger'
  if (priority >= 6) return 'warning'
  if (priority >= 4) return 'success'
  return 'info'
}

const canCancel = (status: string) => {
  return ['pending', 'running'].includes(status)
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const formatDuration = (duration: number) => {
  if (duration < 60) {
    return `${duration}秒`
  } else if (duration < 3600) {
    return `${Math.floor(duration / 60)}分${duration % 60}秒`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}时${minutes}分`
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadTaskList()
})
</script>

<style lang="scss" scoped>
.task-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .filter-section {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      flex: 1;
      
      .filter-select {
        width: 120px;
      }
      
      .date-picker {
        width: 300px;
      }
    }
    
    .filter-right {
      display: flex;
      gap: 8px;
    }
  }
  
  .batch-actions {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px 20px;
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
    
    .batch-info {
      font-size: 14px;
      color: var(--el-color-primary);
    }
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .task-list {
    @include card-style;
    padding: 0;
    
    .task-table {
      .task-info {
        .task-id {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
          font-family: monospace;
        }
        
        .task-script {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-bottom: 2px;
        }
        
        .task-app {
          font-size: 12px;
          color: var(--el-text-color-placeholder);
        }
      }
      
      .duration {
        font-weight: 500;
        color: var(--el-color-primary);
      }
      
      .create-time {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
      
      .text-placeholder {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
      }
      
      :deep(.danger-item) {
        color: var(--el-color-danger);
      }
    }
    
    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
