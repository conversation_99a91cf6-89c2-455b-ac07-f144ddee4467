<template>
  <div class="monitor-overview-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Monitor /></el-icon>
          监控概览
        </h1>
        <p class="page-description">实时监控系统运行状态、性能指标和资源使用情况</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon cpu">
          <el-icon><Cpu /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ systemStats.cpuUsage }}%</div>
          <div class="stat-label">CPU使用率</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon memory">
          <el-icon><MemoryCard /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ systemStats.memoryUsage }}%</div>
          <div class="stat-label">内存使用率</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon disk">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ systemStats.diskUsage }}%</div>
          <div class="stat-label">磁盘使用率</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon network">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ systemStats.networkTraffic }}</div>
          <div class="stat-label">网络流量</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- CPU使用率趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>CPU使用率趋势</h3>
            <el-select v-model="timeRange" size="small" @change="updateCharts">
              <el-option label="最近1小时" value="1h" />
              <el-option label="最近6小时" value="6h" />
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
            </el-select>
          </div>
          <div class="chart-content">
            <v-chart :option="cpuChartOption" style="height: 300px;" />
          </div>
        </div>

        <!-- 内存使用率趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>内存使用率趋势</h3>
          </div>
          <div class="chart-content">
            <v-chart :option="memoryChartOption" style="height: 300px;" />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 应用状态分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>应用状态分布</h3>
          </div>
          <div class="chart-content">
            <v-chart :option="appStatusChartOption" style="height: 300px;" />
          </div>
        </div>

        <!-- 请求量统计 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>API请求量统计</h3>
          </div>
          <div class="chart-content">
            <v-chart :option="requestChartOption" style="height: 300px;" />
          </div>
        </div>
      </div>
    </div>

    <!-- 告警信息 -->
    <div class="alerts-section">
      <div class="section-header">
        <h3>
          <el-icon><Warning /></el-icon>
          最新告警
        </h3>
        <el-button type="text" @click="viewAllAlerts">查看全部</el-button>
      </div>
      <div class="alerts-list">
        <div
          v-for="alert in recentAlerts"
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
        >
          <div class="alert-icon">
            <el-icon v-if="alert.level === 'critical'"><CircleCloseFilled /></el-icon>
            <el-icon v-else-if="alert.level === 'warning'"><WarningFilled /></el-icon>
            <el-icon v-else><InfoFilled /></el-icon>
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-description">{{ alert.description }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-actions">
            <el-button size="small" @click="handleAlert(alert)">处理</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor,
  Refresh,
  Cpu,
  MemoryCard,
  FolderOpened,
  Connection,
  Warning,
  CircleCloseFilled,
  WarningFilled,
  InfoFilled
} from '@element-plus/icons-vue'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'

// 响应式数据
const timeRange = ref('1h')
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 系统统计数据
const systemStats = reactive({
  cpuUsage: 0,
  memoryUsage: 0,
  diskUsage: 0,
  networkTraffic: '0 MB/s'
})

// 最新告警数据
const recentAlerts = ref([
  {
    id: '1',
    level: 'critical',
    title: 'CPU使用率过高',
    description: '服务器CPU使用率超过90%，请及时处理',
    timestamp: new Date().toISOString()
  },
  {
    id: '2',
    level: 'warning',
    title: '内存使用率告警',
    description: '内存使用率达到80%，建议关注',
    timestamp: new Date(Date.now() - 300000).toISOString()
  }
])

// CPU使用率图表配置
const cpuChartOption = ref({
  title: {
    show: false
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>CPU使用率: {c}%'
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [{
    name: 'CPU使用率',
    type: 'line',
    smooth: true,
    data: [],
    itemStyle: {
      color: '#409EFF'
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(64, 158, 255, 0.3)'
        }, {
          offset: 1, color: 'rgba(64, 158, 255, 0.1)'
        }]
      }
    }
  }]
})

// 内存使用率图表配置
const memoryChartOption = ref({
  title: {
    show: false
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>内存使用率: {c}%'
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [{
    name: '内存使用率',
    type: 'line',
    smooth: true,
    data: [],
    itemStyle: {
      color: '#67C23A'
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(103, 194, 58, 0.3)'
        }, {
          offset: 1, color: 'rgba(103, 194, 58, 0.1)'
        }]
      }
    }
  }]
})

// 应用状态分布图表配置
const appStatusChartOption = ref({
  title: {
    show: false
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a}<br/>{b}: {c} ({d}%)'
  },
  series: [{
    name: '应用状态',
    type: 'pie',
    radius: '60%',
    data: [
      { value: 15, name: '运行中', itemStyle: { color: '#67C23A' } },
      { value: 3, name: '已停止', itemStyle: { color: '#F56C6C' } },
      { value: 2, name: '部署中', itemStyle: { color: '#E6A23C' } },
      { value: 1, name: '异常', itemStyle: { color: '#909399' } }
    ]
  }]
})

// API请求量图表配置
const requestChartOption = ref({
  title: {
    show: false
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>请求量: {c}'
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '请求量',
    type: 'bar',
    data: [],
    itemStyle: {
      color: '#E6A23C'
    }
  }]
})

/**
 * 格式化时间
 */
const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

/**
 * 生成模拟数据
 */
const generateMockData = () => {
  const now = new Date()
  const timeLabels = []
  const cpuData = []
  const memoryData = []
  const requestData = []
  
  // 根据时间范围生成不同数量的数据点
  let points = 24
  let interval = 60 * 60 * 1000 // 1小时
  
  switch (timeRange.value) {
    case '1h':
      points = 12
      interval = 5 * 60 * 1000 // 5分钟
      break
    case '6h':
      points = 24
      interval = 15 * 60 * 1000 // 15分钟
      break
    case '24h':
      points = 24
      interval = 60 * 60 * 1000 // 1小时
      break
    case '7d':
      points = 7
      interval = 24 * 60 * 60 * 1000 // 1天
      break
  }
  
  for (let i = points - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval)
    timeLabels.push(dayjs(time).format('HH:mm'))
    cpuData.push(Math.floor(Math.random() * 40) + 30) // 30-70%
    memoryData.push(Math.floor(Math.random() * 30) + 40) // 40-70%
    requestData.push(Math.floor(Math.random() * 1000) + 500) // 500-1500
  }
  
  return { timeLabels, cpuData, memoryData, requestData }
}

/**
 * 更新图表数据
 */
const updateCharts = () => {
  const { timeLabels, cpuData, memoryData, requestData } = generateMockData()
  
  cpuChartOption.value.xAxis.data = timeLabels
  cpuChartOption.value.series[0].data = cpuData
  
  memoryChartOption.value.xAxis.data = timeLabels
  memoryChartOption.value.series[0].data = memoryData
  
  requestChartOption.value.xAxis.data = timeLabels
  requestChartOption.value.series[0].data = requestData
}

/**
 * 更新系统统计数据
 */
const updateSystemStats = () => {
  systemStats.cpuUsage = Math.floor(Math.random() * 40) + 30
  systemStats.memoryUsage = Math.floor(Math.random() * 30) + 40
  systemStats.diskUsage = Math.floor(Math.random() * 20) + 60
  systemStats.networkTraffic = `${(Math.random() * 10 + 5).toFixed(1)} MB/s`
}

/**
 * 刷新数据
 */
const refreshData = () => {
  updateSystemStats()
  updateCharts()
  ElMessage.success('数据已刷新')
}

/**
 * 查看所有告警
 */
const viewAllAlerts = () => {
  ElMessage.info('跳转到告警管理页面...')
}

/**
 * 处理告警
 */
const handleAlert = (alert: any) => {
  ElMessage.info(`处理告警: ${alert.title}`)
}

/**
 * 启动定时刷新
 */
const startAutoRefresh = () => {
  refreshTimer.value = setInterval(() => {
    updateSystemStats()
    updateCharts()
  }, 30000) // 30秒刷新一次
}

/**
 * 停止定时刷新
 */
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  refreshData()
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.monitor-overview-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      font-size: 24px;
      color: white;

      &.cpu { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.memory { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
      &.disk { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
      &.network { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    }

    .stat-content {
      .stat-value {
        font-size: 28px;
        font-weight: 600;
        color: #303133;
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: #606266;
        margin-top: 4px;
      }
    }
  }
}

.charts-section {
  margin-bottom: 24px;

  .chart-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .chart-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .chart-content {
      padding: 16px;
    }
  }
}

.alerts-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .alerts-list {
    .alert-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f5f7fa;

      &:last-child {
        border-bottom: none;
      }

      .alert-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 16px;
        color: white;
      }

      &.critical .alert-icon {
        background-color: #f56c6c;
      }

      &.warning .alert-icon {
        background-color: #e6a23c;
      }

      &.info .alert-icon {
        background-color: #409eff;
      }

      .alert-content {
        flex: 1;

        .alert-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .alert-description {
          font-size: 14px;
          color: #606266;
          margin-bottom: 4px;
        }

        .alert-time {
          font-size: 12px;
          color: #909399;
        }
      }

      .alert-actions {
        margin-left: 12px;
      }
    }
  }
}
</style>
