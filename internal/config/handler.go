package config

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// Handler 配置服务处理器
type Handler struct {
	service Service
	logger  Logger
}

// NewHandler 创建新的处理器
func NewHandler(service Service, logger Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// CreateConfig 创建配置
// @Summary 创建配置
// @Description 创建新的配置项
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param config body CreateConfigRequest true "配置信息"
// @Success 201 {object} ConfigResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 409 {object} ErrorResponse "配置已存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs [post]
func (h *Handler) CreateConfig(c *gin.Context) {
	var req CreateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("创建配置请求参数绑定失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户信息
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}
	req.CreatedBy = userID

	config, err := h.service.CreateConfig(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建配置失败", "error", err, "key", req.Key)
		if err.Error() == "配置已存在" {
			c.JSON(http.StatusConflict, ErrorResponse{
				Code:    "CONFIG_EXISTS",
				Message: err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_FAILED",
			Message: "创建配置失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("配置创建成功", "config_id", config.ID, "key", config.Key)
	c.JSON(http.StatusCreated, &ConfigResponse{Config: config})
}

// GetConfig 获取配置
// @Summary 获取配置
// @Description 根据ID获取配置详情
// @Tags 配置管理
// @Produce json
// @Param id path string true "配置ID"
// @Success 200 {object} ConfigResponse "获取成功"
// @Failure 404 {object} ErrorResponse "配置不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/{id} [get]
func (h *Handler) GetConfig(c *gin.Context) {
	configID := c.Param("id")
	if configID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "配置ID不能为空",
		})
		return
	}

	// 通过ID查询配置
	var config *Config
	var err error
	
	// 如果提供了查询参数，使用查询方式
	if key := c.Query("key"); key != "" {
		scope := ConfigScope(c.Query("scope"))
		scopeID := c.Query("scope_id")
		env := c.Query("environment")
		
		config, err = h.service.GetConfig(c.Request.Context(), key, scope, scopeID, env)
	} else {
		// 否则通过ID直接查询（需要实现GetConfigByID方法）
		config, err = h.service.GetConfigByID(c.Request.Context(), configID)
	}

	if err != nil {
		h.logger.Error("获取配置失败", "error", err, "config_id", configID)
		if err.Error() == "配置不存在" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Code:    "CONFIG_NOT_FOUND",
				Message: "配置不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_FAILED",
			Message: "获取配置失败",
			Details: err.Error(),
		})
		return
	}

	response := &ConfigResponse{Config: config}
	
	// 如果用户有权限查看解密值
	if h.canViewDecryptedValue(c, config) {
		if config.Encrypted {
			// TODO: 解密配置值
			response.DecryptedValue = config.Value
		}
	}

	c.JSON(http.StatusOK, response)
}

// ListConfigs 获取配置列表
// @Summary 获取配置列表
// @Description 分页获取配置列表
// @Tags 配置管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Param scope query string false "配置作用域"
// @Param scope_id query string false "作用域ID"
// @Param environment query string false "环境"
// @Param type query string false "配置类型"
// @Success 200 {object} ConfigListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs [get]
func (h *Handler) ListConfigs(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))
	
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	filter := &ConfigFilter{
		Scope:       ConfigScope(c.Query("scope")),
		ScopeID:     c.Query("scope_id"),
		Environment: c.Query("environment"),
		Type:        ConfigType(c.Query("type")),
		Status:      ConfigStatus(c.Query("status")),
		Tags:        c.QueryArray("tags"),
		Page:        page,
		PageSize:    size,
	}

	configs, total, err := h.service.ListConfigs(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取配置列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_FAILED",
			Message: "获取配置列表失败",
			Details: err.Error(),
		})
		return
	}

	// 转换为响应格式
	responses := make([]*ConfigResponse, len(configs))
	for i, config := range configs {
		response := &ConfigResponse{Config: config}
		
		// 检查是否可以查看解密值
		if h.canViewDecryptedValue(c, config) && config.Encrypted {
			// TODO: 解密配置值
			response.DecryptedValue = config.Value
		}
		
		responses[i] = response
	}

	c.JSON(http.StatusOK, &ConfigListResponse{
		Configs: responses,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// UpdateConfig 更新配置
// @Summary 更新配置
// @Description 更新配置信息
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param id path string true "配置ID"
// @Param config body UpdateConfigRequest true "更新信息"
// @Success 200 {object} ConfigResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "配置不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/{id} [put]
func (h *Handler) UpdateConfig(c *gin.Context) {
	configID := c.Param("id")
	if configID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "配置ID不能为空",
		})
		return
	}

	var req UpdateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("更新配置请求参数绑定失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户信息
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}
	req.UpdatedBy = userID

	config, err := h.service.UpdateConfig(c.Request.Context(), configID, &req)
	if err != nil {
		h.logger.Error("更新配置失败", "error", err, "config_id", configID)
		if err.Error() == "配置不存在" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Code:    "CONFIG_NOT_FOUND",
				Message: "配置不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_FAILED",
			Message: "更新配置失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("配置更新成功", "config_id", configID)
	c.JSON(http.StatusOK, &ConfigResponse{Config: config})
}

// DeleteConfig 删除配置
// @Summary 删除配置
// @Description 删除指定配置
// @Tags 配置管理
// @Produce json
// @Param id path string true "配置ID"
// @Success 204 "删除成功"
// @Failure 404 {object} ErrorResponse "配置不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/{id} [delete]
func (h *Handler) DeleteConfig(c *gin.Context) {
	configID := c.Param("id")
	if configID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "配置ID不能为空",
		})
		return
	}

	err := h.service.DeleteConfig(c.Request.Context(), configID)
	if err != nil {
		h.logger.Error("删除配置失败", "error", err, "config_id", configID)
		if err.Error() == "配置不存在" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Code:    "CONFIG_NOT_FOUND",
				Message: "配置不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DELETE_FAILED",
			Message: "删除配置失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("配置删除成功", "config_id", configID)
	c.Status(http.StatusNoContent)
}

// SearchConfigs 搜索配置
// @Summary 搜索配置
// @Description 根据关键词搜索配置
// @Tags 配置管理
// @Produce json
// @Param q query string true "搜索关键词"
// @Param scope query string false "配置作用域"
// @Param scope_id query string false "作用域ID"
// @Success 200 {object} ConfigListResponse "搜索成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/search [get]
func (h *Handler) SearchConfigs(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "搜索关键词不能为空",
		})
		return
	}

	scope := ConfigScope(c.Query("scope"))
	scopeID := c.Query("scope_id")

	configs, err := h.service.SearchConfigs(c.Request.Context(), query, scope, scopeID)
	if err != nil {
		h.logger.Error("搜索配置失败", "error", err, "query", query)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "SEARCH_FAILED",
			Message: "搜索配置失败",
			Details: err.Error(),
		})
		return
	}

	// 转换为响应格式
	responses := make([]*ConfigResponse, len(configs))
	for i, config := range configs {
		responses[i] = &ConfigResponse{Config: config}
	}

	c.JSON(http.StatusOK, &ConfigListResponse{
		Configs: responses,
		Total:   int64(len(configs)),
		Page:    1,
		Size:    len(configs),
	})
}

// GetEffectiveConfig 获取有效配置
// @Summary 获取有效配置
// @Description 获取考虑继承和优先级的有效配置值
// @Tags 配置管理
// @Produce json
// @Param key query string true "配置键"
// @Param scope query string true "配置作用域"
// @Param scope_id query string false "作用域ID"
// @Param environment query string true "环境"
// @Success 200 {object} map[string]interface{} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "配置不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/effective [get]
func (h *Handler) GetEffectiveConfig(c *gin.Context) {
	key := c.Query("key")
	scope := ConfigScope(c.Query("scope"))
	scopeID := c.Query("scope_id")
	env := c.Query("environment")

	if key == "" || scope == "" || env == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "key, scope, environment 参数不能为空",
		})
		return
	}

	value, err := h.service.GetEffectiveConfig(c.Request.Context(), key, scope, scopeID, env)
	if err != nil {
		h.logger.Error("获取有效配置失败", "error", err, "key", key)
		if err.Error() == "配置不存在" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Code:    "CONFIG_NOT_FOUND",
				Message: "配置不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_FAILED",
			Message: "获取有效配置失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"key":   key,
		"value": value,
	})
}

// BatchGetConfigs 批量获取配置
// @Summary 批量获取配置
// @Description 批量获取多个配置
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param request body BatchGetConfigsRequest true "批量获取请求"
// @Success 200 {object} map[string]interface{} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/batch [get]
func (h *Handler) BatchGetConfigs(c *gin.Context) {
	var req BatchGetConfigsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("批量获取配置请求参数绑定失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	configs, err := h.service.BatchGetConfigs(c.Request.Context(), req.Keys, req.Scope, req.ScopeID, req.Environment)
	if err != nil {
		h.logger.Error("批量获取配置失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "BATCH_GET_FAILED",
			Message: "批量获取配置失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, configs)
}

// BatchUpdateConfigs 批量更新配置
// @Summary 批量更新配置
// @Description 批量更新多个配置
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param configs body []UpdateConfigRequest true "批量更新请求"
// @Success 200 {object} map[string]string "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/configs/batch [post]
func (h *Handler) BatchUpdateConfigs(c *gin.Context) {
	var configs []*UpdateConfigRequest
	if err := c.ShouldBindJSON(&configs); err != nil {
		h.logger.Error("批量更新配置请求参数绑定失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 设置更新者
	userID := c.GetString("user_id")
	for _, config := range configs {
		config.UpdatedBy = userID
	}

	err := h.service.BatchUpdateConfigs(c.Request.Context(), configs)
	if err != nil {
		h.logger.Error("批量更新配置失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "BATCH_UPDATE_FAILED",
			Message: "批量更新配置失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("批量更新配置成功", "count", len(configs))
	c.JSON(http.StatusOK, gin.H{
		"message": "批量更新成功",
		"count":   len(configs),
	})
}

// canViewDecryptedValue 检查是否可以查看解密值
func (h *Handler) canViewDecryptedValue(c *gin.Context, config *Config) bool {
	// TODO: 实现权限检查逻辑
	// 检查用户是否有查看敏感配置的权限
	userRole := c.GetString("user_role")
	return userRole == "admin" || userRole == "developer"
}
